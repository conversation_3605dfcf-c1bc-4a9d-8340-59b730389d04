PODS:
  - audio_session (0.0.1):
    - Flutter
  - audio_streamer (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_blue_plus_darwin (0.0.2):
    - Flutter
    - FlutterMacOS
  - flutter_pcm_player (0.0.1):
    - Flutter
  - flutter_sound (9.26.0):
    - Flutter
    - flutter_sound_core (= 9.26.0)
  - flutter_sound_core (9.26.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - opus_flutter_ios (0.0.2):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - record_darwin (1.0.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audio_streamer (from `.symlinks/plugins/audio_streamer/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus_darwin (from `.symlinks/plugins/flutter_blue_plus_darwin/darwin`)
  - flutter_pcm_player (from `.symlinks/plugins/flutter_pcm_player/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - opus_flutter_ios (from `.symlinks/plugins/opus_flutter_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - flutter_sound_core

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audio_streamer:
    :path: ".symlinks/plugins/audio_streamer/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus_darwin:
    :path: ".symlinks/plugins/flutter_blue_plus_darwin/darwin"
  flutter_pcm_player:
    :path: ".symlinks/plugins/flutter_pcm_player/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  opus_flutter_ios:
    :path: ".symlinks/plugins/opus_flutter_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  audio_streamer: af49e73eb66f71c4eeabb4bbcb05f122d03a6709
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus_darwin: 3ea4ec9133b377febcc8a70b28cd2d2dc9242bd9
  flutter_pcm_player: 49284f275b09eee4150b941c41cd6f9fa40e6df0
  flutter_sound: 39446c16b0111b4926ff261c5c4d601bc1d5d661
  flutter_sound_core: 938c91404747d79bf14a0c85b90a5d95ed7ed5c4
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  opus_flutter_ios: 3df5f9d5bedf0818e7e2c9b94b7baf8d837101c4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  record_darwin: 3b1a8e7d5c0cbf45ad6165b4d83a6ca643d929c3
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 0fe093299deb0064262102d60dca02991989a74f

COCOAPODS: 1.10.2
