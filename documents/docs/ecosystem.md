---
title: 相关生态
description: Android-xiaozhi项目相关的生态系统和扩展项目
sidebar: false
outline: deep
---

<div class="ecosystem-page">

# 相关生态

<div class="header-content">
  <h2>Android-xiaozhi项目生态系统 🌱</h2>
  <p>探索围绕Android-xiaozhi构建的相关项目和扩展</p>
</div>

## 生态概览

本页面将收集和展示Android-xiaozhi项目相关的生态系统项目，包括：

- 官方扩展和插件
- 社区贡献的项目
- 兼容的硬件设备
- 第三方集成方案
- 示例项目和案例分析

> 🚧 此页面正在建设中...

## 即将推出

我们计划收集和整理以下内容：

- 各种设备上的安装和运行指南
- 与智能家居系统的集成方案
- 定制语音指令和技能的开发教程
- 基于Android-xiaozhi构建的项目案例
- 社区贡献的扩展功能

## 参与贡献

如果您有相关的项目或扩展想要分享，欢迎通过以下方式参与贡献：

1. 在GitHub上提交Pull Request，添加您的项目
2. 在Issues中建议您希望看到的集成或扩展
3. 分享您使用Android-xiaozhi的经验和案例

</div>

<style>
.ecosystem-page {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

.ecosystem-page h1 {
  text-align: center;
  margin-bottom: 1rem;
}

.header-content {
  text-align: center;
  margin-bottom: 3rem;
}

.header-content h2 {
  color: var(--vp-c-brand);
  margin-bottom: 0.5rem;
}

.ecosystem-page h2 {
  margin-top: 3rem;
  padding-top: 1rem;
  border-top: 1px solid var(--vp-c-divider);
}

.ecosystem-page blockquote {
  border-left: 4px solid var(--vp-c-brand);
  padding: 1rem;
  background-color: var(--vp-c-bg-soft);
  margin: 2rem 0;
  border-radius: 0 8px 8px 0;
}
</style> 