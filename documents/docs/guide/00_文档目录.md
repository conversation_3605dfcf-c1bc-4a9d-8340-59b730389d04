# Android-xiaozhi文档目录

本目录包含了 Android-xiaozhi 项目 的全部功能文档，按功能模块进行划分，便于查阅和使用。

项目基于Flutter框架开发，支持iOS、Android、Web等多平台，提供丰富的语音交互和AI对话功能。
视觉识别功能需配置API Key才可使用。

## 基础文档

- [功能概览](功能概览.md) - 应用的主要功能特性介绍
- [01_语音交互模式说明](01_语音交互模式说明.md) - 项目概述、基本使用说明和运行模式
- [02_系统依赖及安装](02_系统依赖及安装.md) - 各平台的系统依赖配置
- [Flutter安装指南](Flutter安装指南.md) - Flutter环境搭建和应用安装配置指南

## 参与贡献

如果您想参与项目开发或提供反馈，请查看以下资源：

- [贡献指南](/contributing) - 如何为项目贡献代码，包括开发流程、代码规范和PR提交流程
- [贡献者名单](/contributors) - 感谢为项目做出贡献的开发者
- [赞助支持](/sponsors/) - 如何赞助项目发展

## 相关生态

- [相关生态](/ecosystem) - AI助手项目的相关生态系统和扩展项目

## 目录结构

```
docs/
├── guide/                     # 指南文档目录
│   ├── 00_文档目录.md              # 本文档
│   ├── 功能概览.md                 # 应用的功能特性介绍
│   ├── 01_语音交互模式说明.md       # 项目基本介绍和使用说明
│   ├── 02_系统依赖及安装.md           # 各平台依赖安装指南
│   ├── Flutter安装指南.md          # Flutter环境搭建和应用安装配置指南
│   ├── 功能概览.md                 # 软件端的功能信息
│
├── contributing.md            # 贡献指南
├── contributors.md            # 贡献者名单
├── ecosystem.md               # 相关生态系统
└── sponsors/                  # 赞助相关文档
```

## 版本信息

文档最后更新时间：2025年04月 