# 语音交互模式说明

## 项目概述

Android-xiaozhi是一个智能语音交互助手，基于Flutter开发，专为Android平台优化。支持多种操作模式和功能，包括语音对话、文本聊天以及丰富的AI交互能力。本文档主要介绍语音交互的基本使用方法。

## 语音交互模式

语音交互支持两种模式，您可以根据实际需求选择合适的交互方式：

### 1. 长按对话模式

- **操作方法**：按住说话按钮，松手发送
- **适用场景**：短句交流，精确控制对话开始和结束时间
- **优点**：避免误触发，控制精确
- **取消方式**：长按时上滑即可取消发送
- **震动反馈**：提供触感反馈，确认操作已被识别

### 2. 自动对话模式

- **操作方法**：点击开始对话，系统自动检测语音并发送
- **适用场景**：长句交流，无需手动控制
- **优点**：解放双手，自然交流
- **界面提示**：显示"聆听中"表示系统正在接收您的语音
- **波形动画**：根据声音强度实时显示波形动画，直观反馈

### 模式切换

- 在界面右下角显示当前模式
- 点击按钮可以切换模式
- 可以通过设置菜单设置默认模式

## 对话控制

### 打断功能

当系统正在语音回复时，您可以随时打断：
- 使用界面上的打断按钮

### 状态流转

语音交互系统有以下几种状态：

```
                        +----------------+
                        |                |
                        v                |
+------+    按钮      +------------+   |   +------------+
| IDLE | -----------> | CONNECTING | --+-> | LISTENING  |
+------+              +------------+       +------------+
   ^                                            |
   |                                            | 语音识别完成
   |          +------------+                    v
   +--------- |  SPEAKING  | <-----------------+
     完成播放 +------------+
```

- **IDLE**：空闲状态，等待按钮触发
- **CONNECTING**：正在连接服务器
- **LISTENING**：正在聆听用户语音
- **SPEAKING**：系统正在语音回复

## 系统特性

### 声学优化
- **回声消除(AEC)**: 内置声学回声消除技术，避免扬声器输出干扰麦克风输入
- **降噪处理(NS)**: 实时降噪，过滤环境噪音，提高语音识别准确率
- **高刷新率支持**: 自动检测并适配高刷新率屏幕，提供流畅界面体验

### 性能优化
- 低功耗录音模式，延长电池使用时间
- 智能语音压缩，节省数据流量
- 后台预加载资源，减少等待时间

## 获取帮助

如果遇到问题：

1. 优先查看 GitHub Issues 是否有类似问题得到解决
2. 通过 GitHub Issues 提交问题
3. 联系作者