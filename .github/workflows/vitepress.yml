# workflow 名称，可以自定义
name: Deploy GitHub Pages

# 触发条件：在代码 push 到 master 分支后，自动执行该 workflow
on:
  push:
    branches:
      - main

# 任务
jobs:
  build-and-deploy:
    # 服务器环境：最新版 Ubuntu，也可以自定义版本
    runs-on: ubuntu-latest
    steps:
      # 拉取代码
      - name: Checkout
        uses: actions/checkout@v2
        with: 
          fetch-depth: 0

      # 设置 Node.js 版本
      - name: Setup Node.js environment
        uses: actions/setup-node@v1
        with:
          node-version: "18.20.3"
      # 安装yarn
      - name: Install yarn
        run: npm i yarn -g

      # 如果缓存没有命中，安装依赖
      - name: Install dependencies
        run: cd documents && yarn install

      # 生成静态文件
      - name: Build
        run: cd documents && yarn docs:build

      # 部署到 GitHub Pages
      - name: Deploy
        uses: crazy-max/ghaction-github-pages@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          target-branch: gh-pages # 部署到 gh-pages 分支，main 分支存放的是项目源码，而 gh-pages 分支则用来存放生成的静态文件
          build_dir: documents/docs/.vitepress/dist # vuepress 生成的静态文件存放的地方